absl-py==2.1.0
accelerate==0.33.0
addict==2.4.0
aiofiles==23.2.1
aiohappyeyeballs==2.4.3
aiohttp==3.10.10
aiosignal==1.3.1
alembic==1.13.3
aliyun-python-sdk-core==2.16.0
aliyun-python-sdk-kms==2.16.5
altair==5.4.1
annotated-types==0.7.0
antlr4-python3-runtime==4.9.3
anyio==4.6.2.post1
argon2-cffi==23.1.0
argon2-cffi-bindings==21.2.0
arrow==1.3.0
asttokens==2.4.1
async-lru==2.0.4
async-timeout==4.0.3
attrs==24.2.0
audioread==3.0.1
auto_gptq==0.7.1
autopage==0.5.2
av==13.1.0
babel==2.16.0
beautifulsoup4==4.12.3
bibtexparser==2.0.0b7
bitsandbytes==0.43.1
bleach==6.1.0
certifi==2024.8.30
cffi==1.17.1
cfgv==3.4.0
charset-normalizer==3.4.0
click==8.1.7
cliff==4.7.0
clldutils==3.23.1
cloudpickle==3.1.0
cmaes==0.11.1
cmd2==2.5.0
colorama==0.4.6
coloredlogs==15.0.1
colorlog==6.8.2
comm==0.2.2
compressed-tensors==0.8.0
comtypes==1.4.8
conformer==0.3.2
contourpy==1.3.0
crcmod==1.7
cryptography==43.0.3
csvw==3.5.1
cycler==0.12.1
Cython==3.0.11
datasets==2.18.0
debugpy==1.8.7
decorator==5.1.1
defusedxml==0.7.1
diffusers==0.29.2
dill==0.3.8
diskcache==5.6.3
distlib==0.3.9
distro==1.9.0
dlinfo==1.2.1
edge-tts==6.1.17
editdistance==0.8.1
einops==0.8.0
einx==0.3.0
exceptiongroup==1.2.2
executing==2.1.0
fastapi==0.115.3
fastjsonschema==2.20.0
ffmpeg==1.4
ffmpy==0.4.0
filelock==3.13.1
fire==0.7.0
flatbuffers==24.3.25
fonttools==4.54.1
fqdn==1.5.1
frozendict==2.4.6
frozenlist==1.5.0
fsspec==2024.2.0
funasr==1.1.12
gast==0.6.0
gdown==5.2.0
gekko==1.2.1
gguf==0.10.0
gradio==3.43.2
gradio_client==0.5.0
greenlet==3.1.1
grpcio==1.67.0
h11==0.14.0
html5lib-modern==1.2
httpcore==1.0.6
httptools==0.6.4
httpx==0.27.2
huggingface-hub==0.26.1
humanfriendly==10.0
hydra-colorlog==1.2.0
hydra-core==1.3.2
hydra-optuna-sweeper==1.2.0
HyperPyYAML==1.2.2
identify==2.6.1
idna==3.10
importlib_metadata==8.5.0
importlib_resources==6.4.5
inflect==7.4.0
iniconfig==2.0.0
intel-openmp==2021.4.0
interegular==0.3.3
ipykernel==6.29.5
ipython==8.29.0
ipywidgets==8.1.5
isodate==0.7.2
isoduration==20.11.0
jaconv==0.4.0
jamo==0.4.1
jedi==0.19.1
jieba==0.42.1
Jinja2==3.1.3
jiter==0.7.1
jmespath==0.10.0
joblib==1.4.2
json5==0.9.25
jsonpointer==3.0.0
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter-events==0.10.0
jupyter-lsp==2.2.5
jupyter_client==8.6.3
jupyter_core==5.7.2
jupyter_server==2.14.2
jupyter_server_terminals==0.5.3
jupyterlab==4.2.5
jupyterlab_pygments==0.3.0
jupyterlab_server==2.27.3
jupyterlab_widgets==3.0.13
kaldiio==2.18.0
kiwisolver==1.4.7
langdetect==1.0.9
langid==1.1.6
language-tags==1.2.0
lark==1.2.2
lazy_loader==0.4
librosa==0.10.2.post1
lightning==2.2.4
lightning-utilities==0.11.8
llvmlite==0.43.0
lm-format-enforcer==0.10.9
loguru==0.7.2
lxml==5.3.0
Mako==1.3.6
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==2.1.5
matcha-tts==*******
matplotlib==3.9.2
matplotlib-inline==0.1.7
mdurl==0.1.2
mistral_common==1.5.1
mistune==3.0.2
mkl==2021.4.0
mmengine-lite==0.10.5
modelscope==1.15.0
more-itertools==10.5.0
mpmath==1.3.0
msgpack==1.1.0
msgspec==0.18.6
multidict==6.1.0
multiprocess==0.70.16
narwhals==1.10.0
natsort==8.4.0
nbclient==0.10.0
nbconvert==7.16.4
nbformat==5.10.4
nest-asyncio==1.6.0
networkx==3.2.1
nodeenv==1.9.1
notebook==7.2.2
notebook_shim==0.2.4
numba==0.60.0
numpy==1.26.3
nvidia-ml-py==12.560.30
omegaconf==2.3.0
onnxruntime==1.19.2
openai==1.55.0
openai-whisper==20231117
opencv-contrib-python==*********
opencv-python==*********
opencv-python-headless==*********
optimum==1.23.2
optimum-habana==1.14.0
optuna==2.10.1
orjson==3.10.10
oss2==2.19.1
outlines==0.0.46
overrides==7.7.0
packaging==24.1
pandas==2.2.3
pandocfilters==1.5.1
parso==0.8.4
partial-json-parser==*******.post4
pbr==6.1.0
peft==0.11.1
phonemizer==3.3.0
pillow==10.4.0
platformdirs==4.3.6
pluggy==1.5.0
pooch==1.8.2
pre_commit==4.0.1
prettytable==3.11.0
prometheus-fastapi-instrumentator==7.0.0
prometheus_client==0.21.0
prompt_toolkit==3.0.48
propcache==0.2.0
protobuf==5.28.3
psutil==6.1.0
pure_eval==0.2.3
py-cpuinfo==9.0.0
pyairports==2.1.1
pyarrow==17.0.0
pyarrow-hotfix==0.6
PyAudio==0.2.14
pycountry==24.6.1
pycparser==2.22
pycryptodome==3.21.0
pydantic==2.9.2
pydantic_core==2.23.4
pydub==0.25.1
pygame==2.6.1
Pygments==2.18.0
pylatexenc==2.10
pynini @ file:///D:/bld/pynini_1706391977451/work
pynndescent==0.5.13
pyparsing==3.2.0
pyperclip==1.9.0
pypinyin==0.53.0
pypiwin32==223
pyreadline3==3.5.4
PySocks==1.7.1
pytest==8.3.3
python-dateutil==2.9.0.post0
python-dotenv==1.0.1
python-json-logger==2.0.7
python-multipart==0.0.12
pytorch-lightning==2.4.0
pytorch-wpe==0.0.1
pyttsx3==2.98
pytz==2024.2
pywin32==308
pywinpty==2.0.14
PyYAML==6.0.2
pyzmq==26.2.0
qwen-vl-utils==0.0.8
ray==2.39.0
rdflib==7.1.0
referencing==0.35.1
regex==2024.9.11
requests==2.32.3
rfc3339-validator==0.1.4
rfc3986==1.5.0
rfc3986-validator==0.1.1
rich==13.9.3
rootutils==1.0.7
rouge==1.0.1
rpds-py==0.20.0
ruamel.yaml==0.18.6
ruamel.yaml.clib==0.2.12
ruff==0.7.1
safetensors==0.4.5
scikit-learn==1.5.2
scipy==1.14.1
seaborn==0.13.2
segments==2.2.1
semantic-version==2.10.0
Send2Trash==1.8.3
sentence-transformers==3.0.1
sentencepiece==0.2.0
shellingham==1.5.4
shortuuid==1.0.13
simplejson==3.19.3
six==1.16.0
sniffio==1.3.1
sortedcontainers==2.4.0
sounddevice==0.5.1
soundfile==0.12.1
soupsieve==2.6
soxr==0.5.0.post1
SQLAlchemy==2.0.36
stack-data==0.6.3
starlette==0.41.0
stevedore==5.3.0
sympy==1.13.1
tabulate==0.9.0
tbb==2021.11.0
tensorboard==2.18.0
tensorboard-data-server==0.7.2
tensorboardX==*******
termcolor==2.5.0
terminado==0.18.1
threadpoolctl==3.5.0
tiktoken==0.7.0
tinycss2==1.4.0
tokenizers==0.20.1
tomli==2.0.2
tomlkit==0.12.0
torch==2.5.1+cu121
torch-complex==0.4.4
torchaudio==2.5.1+cu121
torchmetrics==1.5.1
torchvision==0.20.1+cu121
tornado==6.4.1
tqdm==4.66.5
traitlets==5.14.3
transformers==4.45.2
triton @ file:///E:/2_PYTHON/Project/GPT/dependency/triton-3.0.0-cp310-cp310-win_amd64.whl#sha256=5f0ec17adbcbbe60bd9cb67c17d40a6624d9f010c6bcdad989338a7a4c1b352d
typeguard==4.3.0
typer==0.12.5
types-python-dateutil==2.9.0.20241003
typing_extensions==4.12.2
tzdata==2024.2
umap-learn==0.5.6
Unidecode==1.3.8
uri-template==1.3.0
uritemplate==4.1.1
urllib3==2.2.3
uvicorn==0.32.0
vector-quantize-pytorch==1.18.5
virtualenv==20.27.0
vllm==0.6.4.post1
watchfiles==0.24.0
wcwidth==0.2.13
webcolors==24.8.0
webencodings==0.5.1
webrtcvad==2.0.10
websocket-client==1.8.0
websockets==11.0.3
Werkzeug==3.0.6
WeTextProcessing==*******
wget==3.2
whisper==1.1.10
widgetsnbextension==4.0.13
win32-setctime==1.1.0
xformer==1.0.1
xformers==0.0.28.post3
xxhash==3.5.0
yapf==0.40.2
yarl==1.16.0
zipp==3.20.2
